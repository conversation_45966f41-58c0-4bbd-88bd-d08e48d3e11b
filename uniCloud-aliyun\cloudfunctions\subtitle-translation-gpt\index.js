// uniCloud云函数：基于GPT的高质量字幕翻译 - 纯并发处理版本
"use strict";

const createConfig = require("uni-config-center");

// 创建语言配置实例
const languageConfig = createConfig({
  pluginId: 'subtitle-language',
  defaultConfig: {
    languageMap: {},
    languageIdentifierMap: {},
    languageFonts: { default: 'Arial' },
    languageEncodings: {},
    wrapConfig: {
      cjkLanguages: ['zh', 'zh-cn', 'zh-tw', 'ja', 'ko'],
      zh: { maxLineLength: 20, preferredLineLength: 16 },
      western: { maxLineLength: 35, preferredLineLength: 30 }
    },
    punctuationRules: {
      cjk: {
        avoidBreakAfter: [],
        avoidBreakBefore: [],
        preferBreakAfter: []
      },
      western: {
        avoidBreakAfter: [],
        avoidBreakBefore: []
      }
    },
    assStyleConfig: {
      baseStyle: {
        fontName: "Arial",
        primaryColor: "&H0000FFFF",
        secondaryColor: "&H0000FFFF",
        outlineColor: "&*********",
        backColor: "&*********",
        bold: 0,
        italic: 0,
        underline: 0,
        strikeOut: 0,
        scaleX: 100,
        scaleY: 100,
        spacing: 0,
        angle: 0,
        borderStyle: 1,
        outline: 1,
        shadow: 1,
        alignment: 2
      },
      resolutionLevels: {
        "720p": { baseSize: 20, marginBase: 30 },
        "1080p": { baseSize: 42, marginBase: 40 },
        "1440p": { baseSize: 56, marginBase: 50 },
        "4k": { baseSize: 84, marginBase: 60 },
        "8k": { baseSize: 168, marginBase: 80 }
      },
      videoTypeOptimization: {
        horizontal: { fontScale: 1.0, marginScale: 1.0, sideMargin: 20 },
        vertical: { fontScale: 0.6, marginScale: 1.5, sideMargin: 20 },
        square: { fontScale: 1.0, marginScale: 1.0, sideMargin: 20 }
      },
      languageAdjustments: {
        zh: { marginExtra: 10, verticalExtra: 15 },
        ja: { marginExtra: 8, verticalExtra: 12 },
        ko: { marginExtra: 5, verticalExtra: 8 },
        en: { marginExtra: 5, verticalExtra: 8 }
      },
      scriptInfo: {
        title: "Video Translation Subtitle",
        scriptType: "v4.00+",
        wrapStyle: 0,
        scaledBorderAndShadow: "yes",
        autoAdaptive: true
      }
    }
  }
});

// 常量配置
const CONFIG = {
  DEFAULT_MODEL: "gpt-5-mini",
  TEMPERATURE: 0.3,
  MAX_TOKENS: 4000, 
  API_TIMEOUT: 60000 * 2,
  BATCH_SIZE: 50,
  MAX_RETRIES: 3,
  MAX_CONCURRENT_BATCHES: 10,
};

// ASS字幕样式配置现在通过 uni-config-center/subtitle-language 模块管理


/**
 * 标准化语言标识符
 * @param {string} languageIdentifier - 原始语言标识符
 * @returns {string} 标准化后的语言代码
 */
function normalizeLanguageIdentifier(languageIdentifier) {
  if (!languageIdentifier) return "en"; // 默认英文

  const languageMap = languageConfig.config('languageMap') || {};
  const identifierMap = languageConfig.config('languageIdentifierMap') || {};

  // 直接匹配标准代码
  if (languageMap[languageIdentifier]) {
    return languageIdentifier;
  }

  // 通过映射表转换
  const normalizedCode = identifierMap[languageIdentifier];
  if (normalizedCode) {
    return normalizedCode;
  }

  // 尝试小写匹配
  const lowerCase = languageIdentifier.toLowerCase();
  const lowerCaseMatch = identifierMap[lowerCase];
  if (lowerCaseMatch) {
    return lowerCaseMatch;
  }

  console.warn(`未知的语言标识符: ${languageIdentifier}，使用默认值 en`);
  return "en"; // 默认返回英文
}

/**
 * 并发翻译字幕条目 - 使用Promise.all
 */
async function translateSubtitlesBatchOptimized(
  entries,
  apiKey,
  baseUrl,
  model,
  sourceLanguage,
  targetLanguage
) {
  const translationStartTime = Date.now();
  console.log("开始并发翻译字幕", {
    totalEntries: entries.length,
    batchSize: CONFIG.BATCH_SIZE,
    maxConcurrent: CONFIG.MAX_CONCURRENT_BATCHES,
    sourceLanguage,
    targetLanguage,
    model,
  });

  // 过滤有效文本条目
  const validEntries = [];
  entries.forEach((entry, index) => {
    if (entry.text?.trim()) {
      validEntries.push({ ...entry, originalIndex: index });
    }
  });

  if (validEntries.length === 0) {
    console.log("没有有效的字幕文本需要翻译");
    return entries;
  }

  console.log(`有效字幕条目: ${validEntries.length}/${entries.length}`);

  // 将有效条目分批处理
  const batches = [];
  for (let i = 0; i < validEntries.length; i += CONFIG.BATCH_SIZE) {
    batches.push(validEntries.slice(i, i + CONFIG.BATCH_SIZE));
  }

  console.log(`分为 ${batches.length} 批处理，每批最多 ${CONFIG.BATCH_SIZE} 条`);

  // 复制原数组用于存储翻译结果
  const translatedEntries = [...entries];

  // 纯并发处理
  console.log(`启用纯并发处理，最大并发数: ${CONFIG.MAX_CONCURRENT_BATCHES}`);
  const totalTranslatedCount = await processBatchesConcurrently(
    batches,
    translatedEntries,
    apiKey,
    baseUrl,
    model,
    targetLanguage
  );

  const totalTime = (Date.now() - translationStartTime) / 1000;
  console.log(`并发翻译完成`, {
    totalEntries: entries.length,
    validEntries: validEntries.length,
    translatedCount: totalTranslatedCount,
    batchCount: batches.length,
    processingTime: `${totalTime.toFixed(2)}秒`,
    successRate: `${((totalTranslatedCount / validEntries.length) * 100).toFixed(1)}%`,
  });

  return translatedEntries;
}

/**
 * 并发处理多个批次 - 使用Promise.all
 */
async function processBatchesConcurrently(
  batches,
  translatedEntries,
  apiKey,
  baseUrl,
  model,
  targetLanguage
) {
  let totalTranslatedCount = 0;
  const maxConcurrent = Math.min(CONFIG.MAX_CONCURRENT_BATCHES, batches.length);

  // 将批次分组，每组最多包含 maxConcurrent 个批次
  for (let i = 0; i < batches.length; i += maxConcurrent) {
    const batchGroup = batches.slice(i, i + maxConcurrent);
    const groupStartTime = Date.now();

    console.log(
      `并发处理第 ${i + 1}-${Math.min(i + maxConcurrent, batches.length)} 批次（共 ${
        batchGroup.length
      } 个并发）`
    );

    // 创建并发任务 - 使用Promise.all
    const concurrentTasks = batchGroup.map(async (batch, groupIndex) => {
      const actualBatchIndex = i + groupIndex + 1;
      try {
        const batchResult = await translateBatchWithRetry(
          batch,
          apiKey,
          baseUrl,
          model,
          targetLanguage,
          actualBatchIndex
        );
        return { success: true, batchResult, batch, batchIndex: actualBatchIndex };
      } catch (error) {
        return { success: false, error, batch, batchIndex: actualBatchIndex };
      }
    });

    // 等待当前组的所有批次完成
    const results = await Promise.all(concurrentTasks);

    // 处理结果
    results.forEach(({ success, batchResult, batch, batchIndex, error }) => {
      if (success) {
        // 将翻译结果合并到最终数组中
        batchResult.forEach((translatedEntry) => {
          if (translatedEntry.originalIndex !== undefined) {
            translatedEntries[translatedEntry.originalIndex] = translatedEntry;
            totalTranslatedCount++;
          }
        });
        console.log(`第 ${batchIndex} 批翻译完成`);
      } else {
        console.error(`第 ${batchIndex} 批翻译失败:`, error.message);
        // 批次失败时保留原文
        batch.forEach((entry) => {
          if (entry.originalIndex !== undefined) {
            translatedEntries[entry.originalIndex] = entry;
          }
        });
      }
    });

    const groupTime = (Date.now() - groupStartTime) / 1000;
    console.log(`并发组处理完成，耗时: ${groupTime.toFixed(2)}秒`);
  }

  return totalTranslatedCount;
}

/**
 * 带重试机制的单批次翻译函数
 */
async function translateBatchWithRetry(batch, apiKey, baseUrl, model, targetLanguage, batchNumber) {
  let lastError = null;

  for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
    try {
      console.log(`第 ${batchNumber} 批第 ${attempt} 次尝试翻译...`);

      const result = await translateSingleBatch(batch, apiKey, baseUrl, model, targetLanguage);

      console.log(`第 ${batchNumber} 批第 ${attempt} 次尝试成功`);
      return result;
    } catch (error) {
      lastError = error;
      console.error(`第 ${batchNumber} 批第 ${attempt} 次尝试失败:`, error.message);

      if (attempt < CONFIG.MAX_RETRIES) {
        console.log(`立即重试第 ${attempt + 1} 次...`);
      }
    }
  }

  // 所有重试都失败，抛出最后一个错误
  throw new Error(
    `第 ${batchNumber} 批翻译失败，已重试 ${CONFIG.MAX_RETRIES} 次: ${lastError.message}`
  );
}

/**
 * 翻译单个批次的字幕条目
 */
async function translateSingleBatch(batch, apiKey, baseUrl, model, targetLanguage) {
  // 构建批次文本
  const batchTexts = batch.map((entry, index) => `${index + 1}. ${entry.text.trim()}`);
  const combinedText = batchTexts.join("\n");

  console.log(`批次文本长度: ${combinedText.length} 字符，包含 ${batch.length} 条字幕`);

  // 获取目标语言名称
  const languageMap = languageConfig.config('languageMap') || {};
  const targetLangName = languageMap[targetLanguage] || targetLanguage.toUpperCase();

  // 构建翻译请求
  const requestBody = {
    model,
    messages: [
      {
        role: "system",
        content: `你是一位资深的多语言翻译专家，将带编号文本翻译成地道流畅的${targetLangName}。要求：保持编号格式，逐行对应翻译，忠实原意，自然表达。仅输出译文。`,
      },
      {
        role: "user",
        content: `翻译成${targetLangName}：\n\n${combinedText}`,
      },
    ],
    temperature: CONFIG.TEMPERATURE,
    max_tokens: CONFIG.MAX_TOKENS,
  };

  // 调用GPT API
  const apiStartTime = Date.now();
  const response = await uniCloud.httpclient.request(`${baseUrl}/v1/chat/completions`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    data: JSON.stringify(requestBody),
    dataType: "json",
    timeout: CONFIG.API_TIMEOUT,
  });

  const apiTime = (Date.now() - apiStartTime) / 1000;
  console.log(`API响应耗时: ${apiTime.toFixed(2)}秒`);

  if (response.status !== 200) {
    let errorMessage = `GPT API请求失败，状态码: ${response.status}`;
    if (response.data?.error?.message) {
      errorMessage += ` - ${response.data.error.message}`;
    }
    throw new Error(errorMessage);
  }

  const result = response.data;
  if (!result.choices?.length || !result.choices[0].message?.content) {
    throw new Error("GPT API返回空结果或格式错误");
  }

  const translatedText = result.choices[0].message.content.trim();
  if (!translatedText) {
    throw new Error("GPT翻译返回空白内容");
  }

  // 解析翻译结果
  const translatedLines = translatedText
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line);

  console.log(`解析翻译结果，共 ${translatedLines.length} 行`);

  // 匹配翻译结果到原始条目
  const translatedBatch = [...batch];
  batch.forEach((entry, index) => {
    const targetPattern = `${index + 1}.`;
    let translatedLine = null;

    // 查找对应的翻译
    for (const line of translatedLines) {
      if (line.startsWith(targetPattern)) {
        translatedLine = line.substring(targetPattern.length).trim();
        break;
      }
    }

    if (translatedLine) {
      translatedBatch[index] = {
        ...entry,
        text: translatedLine,
      };
    }
    // 如果没找到翻译，保留原文（translatedBatch[index] 已经是原始条目）
  });

  return translatedBatch;
}

// 辅助函数
function createSuccessResponse(message, data) {
  return { code: 200, message, data, timestamp: new Date().toISOString() };
}

function createErrorResponse(code, message, extra = {}) {
  return { code, message, timestamp: new Date().toISOString(), ...extra };
}

async function validateAndGetTask(tasksCollection, taskId, sourceLanguage) {
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data?.length) {
    throw new Error("任务不存在");
  }

  const task = taskInfo.data[0];
  if (!task.subtitleOssUrl) {
    throw new Error("缺少字幕文件地址");
  }

  // 标准化语言标识符
  const rawSourceLanguage = sourceLanguage === "auto" ? task.detectedLanguage || "en" : sourceLanguage;
  const actualSourceLanguage = normalizeLanguageIdentifier(rawSourceLanguage);

  console.log(`语言标识符标准化: ${rawSourceLanguage} -> ${actualSourceLanguage}`);
  return { task, actualSourceLanguage };
}

async function getAndValidateGptConfig() {
  const gptConfig = createConfig({
    pluginId: "openai-api",
    defaultConfig: {
      baseUrl: "https://aihubmix.com",
      model: CONFIG.DEFAULT_MODEL,
    },
  });

  const apiKey = gptConfig.config("apiKey");
  const baseUrl = gptConfig.config("baseUrl");

  if (!apiKey) {
    throw new Error("GPT API配置缺失，请检查apiKey");
  }

  return { apiKey, baseUrl };
}

async function downloadSrtFromOSS(ossUrl) {
  if (!ossUrl || typeof ossUrl !== "string") {
    throw new Error(`无效的OSS URL: ${ossUrl}`);
  }

  const response = await uniCloud.httpclient.request(ossUrl, {
    method: "GET",
    timeout: 30000,
  });

  if (response.status !== 200) {
    throw new Error(`下载SRT文件失败，状态码: ${response.status}`);
  }

  let srtContent = response.data;
  if (Buffer.isBuffer(srtContent)) {
    srtContent = srtContent.toString("utf8");
  } else if (typeof srtContent !== "string") {
    srtContent = String(srtContent || "");
  }

  if (!srtContent.trim()) {
    throw new Error("下载的SRT文件内容为空");
  }

  return srtContent;
}

function parseSRT(srtContent) {
  if (!srtContent || !srtContent.trim()) {
    throw new Error("SRT字幕内容为空");
  }

  const entries = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    if (!block?.trim()) continue;

    const lines = block.trim().split("\n");
    if (lines.length >= 3) {
      const index = parseInt(lines[0]);
      const timeRange = lines[1];
      const text = lines.slice(2).join("\n").trim();

      if (!isNaN(index) && timeRange && text) {
        entries.push({ index, timeRange, text });
      }
    }
  }

  console.log(`SRT解析完成，共 ${entries.length} 条有效字幕`);
  return entries;
}

/**
 * 生成ASS字幕格式（完全复用现有entries结构）
 * @param {Array} entries - 复用parseSRT()返回的字幕条目数组
 * @param {string} targetLanguage - 复用现有目标语言参数
 * @param {Object} styleOverrides - 可选的样式覆盖配置
 * @returns {string} ASS格式字符串
 */
function generateASS(entries, targetLanguage = "zh", styleOverrides = {}) {
  const encoding = getLanguageEncoding(targetLanguage);

  // 获取ASS样式配置
  const assStyleConfig = languageConfig.config('assStyleConfig') || {};
  const baseStyle = assStyleConfig.baseStyle || {};
  const scriptInfo = assStyleConfig.scriptInfo || {};

  // 合并默认配置和覆盖配置
  const style = { ...baseStyle, ...styleOverrides };

  // 生成自适应分辨率的ASS头部
  const resolutionConfig = scriptInfo.autoAdaptive
    ? "" // 自适应模式：不设置固定分辨率，让播放器自动适配
    : `PlayResX: 1920\nPlayResY: 1080`;

  const assHeader = `[Script Info]
Title: ${scriptInfo.title}
ScriptType: ${scriptInfo.scriptType}
WrapStyle: ${scriptInfo.wrapStyle}
ScaledBorderAndShadow: ${scriptInfo.scaledBorderAndShadow}
${resolutionConfig}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,${style.fontName},${style.fontSize},${style.primaryColor},${style.secondaryColor},${style.outlineColor},${style.backColor},${style.bold},${style.italic},${style.underline},${style.strikeOut},${style.scaleX},${style.scaleY},${style.spacing},${style.angle},${style.borderStyle},${style.outline},${style.shadow},${style.alignment},${style.marginL},${style.marginR},${style.marginV},${encoding}`;

  const assEvents = entries
    .map((entry, index) => {
      const { startTime, endTime } = convertTimeRange(entry.timeRange);
      // 使用智能换行处理文本
      const processedText = smartTextWrap(entry.text, targetLanguage);
      return `Dialogue: ${index},${startTime},${endTime},Default,,0,0,0,,${processedText}`;
    })
    .join("\n");

  return `${assHeader}\n[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n${assEvents}`;
}

/**
 * 转换时间格式（复用现有timeRange格式）
 * @param {string} timeRange - SRT格式的时间范围
 * @returns {Object} ASS格式的开始和结束时间
 */
function convertTimeRange(timeRange) {
  const [start, end] = timeRange.split(" --> ");
  return {
    startTime: convertSRTTimeToASS(start),
    endTime: convertSRTTimeToASS(end),
  };
}

/**
 * 将SRT时间格式转换为ASS时间格式
 * @param {string} srtTime - SRT时间格式 (HH:MM:SS,mmm)
 * @returns {string} ASS时间格式 (H:MM:SS.cc)
 */
function convertSRTTimeToASS(srtTime) {
  const [time, milliseconds] = srtTime.split(",");
  const centiseconds = Math.floor(parseInt(milliseconds) / 10);
  const [hours, minutes, seconds] = time.split(":");
  return `${parseInt(hours)}:${minutes}:${seconds}.${centiseconds.toString().padStart(2, "0")}`;
}

/**
 * 获取语言编码（简化版）
 * @param {string} targetLanguage - 目标语言代码
 * @returns {number} 对应的字符编码
 */
function getLanguageEncoding(targetLanguage) {
  const encodings = languageConfig.config('languageEncodings') || {};
  return encodings[targetLanguage] || 1;
}

/**
 * 获取语言特定字体
 * @param {string} targetLanguage - 目标语言代码
 * @returns {string} 对应的字体名称
 */
function getLanguageFont(targetLanguage) {
  const fonts = languageConfig.config('languageFonts') || { default: 'Arial' };
  return fonts[targetLanguage] || fonts.default || 'Arial';
}

/**
 * 智能文本换行处理 - 支持中文优化
 * @param {string} text - 原始文本
 * @param {string} targetLanguage - 目标语言（用于选择换行策略）
 * @returns {string} 处理后的文本
 */
function smartTextWrap(text, targetLanguage = "zh") {
  if (!text || !text.trim()) return text;

  // 先处理已有的换行符
  text = text.replace(/\r?\n/g, " ");

  // 根据语言选择换行策略
  if (isCJKLanguage(targetLanguage)) {
    return wrapCJKText(text, targetLanguage);
  } else {
    return wrapWesternText(text);
  }
}

/**
 * 判断是否为CJK语言（中日韩）
 * @param {string} language - 语言代码
 * @returns {boolean}
 */
function isCJKLanguage(language) {
  const wrapConfig = languageConfig.config('wrapConfig') || {};
  const cjkLanguages = wrapConfig.cjkLanguages || ['zh', 'zh-cn', 'zh-tw', 'ja', 'ko'];
  return cjkLanguages.includes(language);
}

/**
 * CJK文本智能换行（中日韩）
 * @param {string} text - 原始文本
 * @param {string} language - 语言代码
 * @returns {string} 换行后的文本
 */
function wrapCJKText(text, language) {
  // 根据分辨率调整换行配置
  const config = getCJKWrapConfig(language);

  // 获取标点符号配置
  const punctuationRules = languageConfig.config('punctuationRules') || {};
  const punctuationConfig = punctuationRules.cjk || {
    avoidBreakAfter: ['\u3001', '\u3002', '\uff0c', '\uff1a', '\uff1b', '\u201c', '\u2018', '\uff08', '\u3010', '\u300a', '\u3008'],
    avoidBreakBefore: ['\u3002', '\uff01', '\uff1f', '\uff0c', '\uff1b', '\uff1a', '\u201d', '\u2019', '\uff09', '\u3011', '\u300b', '\u3009', '\u2026'],
    preferBreakAfter: ['\u3002', '\uff01', '\uff1f', '\uff1b'],
  };

  // 合并配置
  const finalConfig = { ...config, ...punctuationConfig };

  // 如果文本长度在推荐范围内，直接返回
  if (text.length <= finalConfig.preferredLineLength) {
    return text;
  }

  // 如果文本不需要换行，直接返回
  if (text.length <= finalConfig.maxLineLength) {
    return text;
  }

  // 执行智能换行
  return performCJKWrap(text, finalConfig);
}

/**
 * 获取CJK换行配置（根据分辨率调整）
 * @param {string} language - 语言代码
 * @returns {Object} 换行配置
 */
function getCJKWrapConfig(language) {
  const wrapConfig = languageConfig.config('wrapConfig') || {};

  // 获取语言特定配置，如果不存在则使用中文配置作为默认
  const languageSpecific = wrapConfig[language] || wrapConfig.zh || {
    maxLineLength: 20,
    preferredLineLength: 16
  };

  return languageSpecific;
}

/**
 * 执行CJK文本换行
 * @param {string} text - 原始文本
 * @param {Object} config - 换行配置
 * @returns {string} 换行后的文本
 */
function performCJKWrap(text, config) {
  const lines = [];
  let currentLine = '';
  let i = 0;

  while (i < text.length) {
    const char = text[i];
    currentLine += char;

    // 检查是否需要换行
    if (currentLine.length >= config.preferredLineLength) {
      // 寻找最佳换行位置
      const breakPosition = findBestBreakPosition(currentLine, config);

      if (breakPosition > 0) {
        // 在找到的位置换行
        lines.push(currentLine.substring(0, breakPosition));
        currentLine = currentLine.substring(breakPosition);
      } else if (currentLine.length >= config.maxLineLength) {
        // 强制换行
        lines.push(currentLine);
        currentLine = '';
      }
    }

    i++;
  }

  // 添加最后一行
  if (currentLine.trim()) {
    lines.push(currentLine);
  }

  return lines.join('\\N');
}

/**
 * 寻找最佳换行位置
 * @param {string} line - 当前行文本
 * @param {Object} config - 换行配置
 * @returns {number} 最佳换行位置（0表示不换行）
 */
function findBestBreakPosition(line, config) {
  // 从后往前寻找最佳换行位置
  for (let i = line.length - 1; i >= Math.max(1, line.length - 5); i--) {
    const char = line[i - 1];
    const nextChar = line[i];

    // 优先在句号、感叹号、问号后换行
    if (config.preferBreakAfter.includes(char)) {
      return i;
    }

    // 避免在特定标点前后换行
    if (config.avoidBreakAfter.includes(char) || config.avoidBreakBefore.includes(nextChar)) {
      continue;
    }

    // 可以在此位置换行
    return i;
  }

  return 0; // 没有找到合适的换行位置
}

/**
 * 西文文本换行（英文等）
 * @param {string} text - 原始文本
 * @returns {string} 换行后的文本
 */
function wrapWesternText(text) {
  const wrapConfig = languageConfig.config('wrapConfig') || {};
  const westernConfig = wrapConfig.western || { maxLineLength: 35, preferredLineLength: 30 };
  const { maxLineLength, preferredLineLength } = westernConfig;

  if (text.length <= preferredLineLength) {
    return text;
  }

  if (text.length <= maxLineLength) {
    return text;
  }

  // 按单词分割
  const words = text.split(/\s+/);
  const lines = [];
  let currentLine = '';

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word;

    if (testLine.length <= preferredLineLength) {
      currentLine = testLine;
    } else if (testLine.length <= maxLineLength && currentLine) {
      currentLine = testLine;
    } else {
      if (currentLine) {
        lines.push(currentLine);
      }
      currentLine = word;
    }
  }

  if (currentLine) {
    lines.push(currentLine);
  }

  return lines.join('\\N');
}

/**
 * 获取视频分辨率信息（从数据库读取或使用默认值）
 * @param {string} taskId - 任务ID（用于查询视频信息）
 * @param {Object} tasksCollection - 数据库任务集合
 * @returns {Promise<Object>} 包含视频宽度和高度的对象
 */
async function getVideoResolution(taskId, tasksCollection) {
  try {
    if (taskId && tasksCollection) {
      // 从数据库查询视频分辨率信息
      const taskResult = await tasksCollection.doc(taskId).get();
      if (taskResult.data && taskResult.data.length > 0) {
        const task = taskResult.data[0];
        if (task.videoWidth && task.videoHeight) {
          console.log(`从数据库获取视频分辨率: ${task.videoWidth}x${task.videoHeight}`);
          return {
            width: task.videoWidth,
            height: task.videoHeight,
            source: "database",
          };
        }
      }
    }
  } catch (error) {
    console.warn("从数据库获取视频分辨率失败:", error.message);
  }

  // 返回常见的1080p作为默认值
  console.log("使用默认视频分辨率: 1920x1080");
  return {
    width: 1920,
    height: 1080,
    source: "default", // 标记这是默认值而非实际检测值
  };
}

/**
 * 获取分辨率等级
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @returns {string} 分辨率等级
 */
function getResolutionLevel(videoWidth, videoHeight) {
  if (!videoWidth || !videoHeight) return "1080p";

  const minDimension = Math.min(videoWidth, videoHeight);

  if (minDimension <= 720) return "720p";
  if (minDimension <= 1080) return "1080p";
  if (minDimension <= 1440) return "1440p";
  if (minDimension <= 2160) return "4k";
  return "8k";
}

/**
 * 获取视频类型
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @returns {string} 视频类型
 */
function getVideoType(videoWidth, videoHeight) {
  if (!videoWidth || !videoHeight) return "horizontal";

  if (videoHeight > videoWidth) return "vertical";
  if (videoWidth === videoHeight) return "square";
  return "horizontal";
}

/**
 * 统一的字幕样式生成器 - 整合所有样式逻辑
 * @param {string} targetLanguage - 目标语言代码
 * @param {number} videoWidth - 视频宽度（可选）
 * @param {number} videoHeight - 视频高度（可选）
 * @returns {Object} 完整的字幕样式配置
 */
function getLanguageSpecificStyle(targetLanguage, videoWidth = null, videoHeight = null) {
  // 获取基础配置
  const resolutionLevel = getResolutionLevel(videoWidth, videoHeight);
  const videoType = getVideoType(videoWidth, videoHeight);

  // 获取ASS样式配置
  const assStyleConfig = languageConfig.config('assStyleConfig') || {};
  const resolutionLevels = assStyleConfig.resolutionLevels || {};
  const videoTypeOptimization = assStyleConfig.videoTypeOptimization || {};
  const languageAdjustments = assStyleConfig.languageAdjustments || {};

  // 获取配置对象
  const resConfig = resolutionLevels[resolutionLevel] || { baseSize: 42, marginBase: 40 };
  const typeConfig = videoTypeOptimization[videoType] || { fontScale: 1.0, marginScale: 1.0, sideMargin: 20 };
  const langConfig = languageAdjustments[targetLanguage] || { marginExtra: 0, verticalExtra: 0 };

  // 获取语言特定字体
  const languageFont = getLanguageFont(targetLanguage);

  console.log(
    `字幕样式配置: ${resolutionLevel} ${videoType}视频, 语言: ${targetLanguage}, 字体: ${
      languageFont.split(",")[0]
    }`
  );

  // 计算最终样式
  const fontSize = Math.round(resConfig.baseSize * typeConfig.fontScale);
  const marginV =
    Math.round(resConfig.marginBase * typeConfig.marginScale) +
    (videoType === "vertical" ? langConfig.verticalExtra : langConfig.marginExtra);

  // 获取基础样式
  const baseStyle = assStyleConfig.baseStyle || {};

  // 返回完整样式
  return {
    ...baseStyle,
    fontName: languageFont, // 使用语言特定字体
    fontSize: fontSize,
    marginL: typeConfig.sideMargin,
    marginR: typeConfig.sideMargin,
    marginV: marginV,
  };
}

async function uploadTranslatedAssToOSS(taskId, assContent) {
  const OSS = require("ali-oss");

  const aliyunConfig = createConfig({
    pluginId: "aliyun-oss",
    defaultConfig: {
      region: "oss-cn-shanghai",
      bucket: "video--tanslate",
    },
  });

  const accessKeyId = aliyunConfig.config("accessKeyId");
  const accessKeySecret = aliyunConfig.config("accessKeySecret");
  const region = aliyunConfig.config("region");
  const bucketName = aliyunConfig.config("bucket");

  if (!accessKeyId || !accessKeySecret) {
    throw new Error("阿里云OSS配置缺失");
  }

  const client = new OSS({
    accessKeyId,
    accessKeySecret,
    bucket: bucketName,
    region,
  });

  const timestamp = Date.now();
  const objectKey = `subtitle/task_${taskId}_translated_${timestamp}.ass`;

  const uploadResult = await client.put(objectKey, Buffer.from(assContent, "utf8"), {
    headers: {
      "Content-Type": "text/plain; charset=utf-8",
    },
  });

  return {
    subtitleOssUrl: uploadResult.url,
    objectKey,
  };
}

/**
 * 测试中文换行功能
 * @param {string} text - 测试文本
 * @param {string} language - 语言代码
 */
function testChineseWrap(text, language = 'zh') {
  console.log(`\n=== 测试中文换行功能 ===`);
  console.log(`原文: ${text}`);
  console.log(`长度: ${text.length} 字符`);

  const result = smartTextWrap(text, language);
  console.log(`换行后: ${result}`);
  console.log(`行数: ${result.split('\\N').length}`);

  result.split('\\N').forEach((line, index) => {
    console.log(`第${index + 1}行: "${line}" (${line.length}字符)`);
  });
  console.log(`=== 测试结束 ===\n`);

  return result;
}

exports.main = async (event) => {
  const startTime = Date.now();

  try {
    const { taskId, sourceLanguage = "auto", targetLanguage = "zh" } = event;

    // 标准化目标语言标识符
    const normalizedTargetLanguage = normalizeLanguageIdentifier(targetLanguage);

    console.log("subtitle-translation-gpt 云函数启动（纯并发处理版本）");
    console.log("输入参数：", { taskId, sourceLanguage, targetLanguage: normalizedTargetLanguage });
    if (targetLanguage !== normalizedTargetLanguage) {
      console.log(`目标语言标准化: ${targetLanguage} -> ${normalizedTargetLanguage}`);
    }

    if (!taskId) {
      return createErrorResponse(400, "缺少必要参数：taskId");
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 验证和获取任务信息
    const { task, actualSourceLanguage } = await validateAndGetTask(
      tasksCollection,
      taskId,
      sourceLanguage
    );

    // 检查是否需要翻译
    if (actualSourceLanguage === normalizedTargetLanguage) {
      console.log(`源语言(${actualSourceLanguage})和目标语言(${normalizedTargetLanguage})相同，跳过翻译但继续处理字幕文件`);

      // 即使语言相同，也需要完成完整流程：下载、解析、生成ASS、上传、更新状态
      console.log("开始下载字幕文件...");
      const srtContent = await downloadSrtFromOSS(task.subtitleOssUrl);

      console.log("解析SRT字幕文件...");
      const subtitleEntries = parseSRT(srtContent);
      if (subtitleEntries.length === 0) {
        throw new Error("字幕文件为空或格式错误");
      }
      console.log(`解析完成，共 ${subtitleEntries.length} 条字幕`);

      // 生成和上传ASS字幕文件（使用原始字幕，无需翻译）
      console.log("生成并上传字幕文件...");

      // 获取视频分辨率信息
      const videoResolution = await getVideoResolution(taskId, tasksCollection);
      const languageSpecificStyle = getLanguageSpecificStyle(
        normalizedTargetLanguage,
        videoResolution.width,
        videoResolution.height
      );
      console.log(
        `使用${normalizedTargetLanguage}语言优化样式: 动态字体${languageSpecificStyle.fontSize}px, 边距${languageSpecificStyle.marginV}px, 分辨率${videoResolution.width}x${videoResolution.height}(${videoResolution.source})`
      );

      const assContent = generateASS(
        subtitleEntries, // 使用原始字幕，无需翻译
        normalizedTargetLanguage,
        languageSpecificStyle
      );
      const uploadResult = await uploadTranslatedAssToOSS(taskId, assContent);

      // 更新任务状态为merging，准备字幕烧录
      await tasksCollection.doc(taskId).update({
        status: "merging",
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        translationStarted: false,
        updateTime: new Date(),
      });

      console.log("字幕处理完成，准备启动字幕烧录");

      // 直接启动字幕烧录
      try {
        const mergeResult = await uniCloud.callFunction({
          name: "process-video-task",
          data: { taskId, action: "merge_subtitle" },
        });
        console.log("字幕烧录启动结果：", mergeResult.result);
      } catch (error) {
        console.error("字幕烧录启动异常：", error.message);
      }

      const processingTime = (Date.now() - startTime) / 1000;
      console.log(`字幕处理任务完成，耗时: ${processingTime.toFixed(2)}秒`);

      return createSuccessResponse("语言相同，字幕处理成功", {
        taskId,
        status: "completed",
        reason: "same_language",
        translatedCount: subtitleEntries.length,
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        processingTime,
        sourceLanguage: actualSourceLanguage,
        targetLanguage: normalizedTargetLanguage,
      });
    }

    console.log(`开始翻译流程：${actualSourceLanguage} -> ${normalizedTargetLanguage}`);

    // 获取和验证API配置
    const { apiKey, baseUrl } = await getAndValidateGptConfig();
    const model = CONFIG.DEFAULT_MODEL;

    console.log("GPT配置验证通过", { baseUrl, model, hasApiKey: !!apiKey });

    // 执行翻译流程
    console.log("开始下载字幕文件...");
    const srtContent = await downloadSrtFromOSS(task.subtitleOssUrl);

    console.log("解析SRT字幕文件...：", srtContent);
    const subtitleEntries = parseSRT(srtContent);
    if (subtitleEntries.length === 0) {
      throw new Error("字幕文件为空或格式错误");
    }
    console.log(`解析完成，共 ${subtitleEntries.length} 条字幕`);

    // 执行纯并发翻译
    console.log("开始纯并发翻译...");
    const translatedEntries = await translateSubtitlesBatchOptimized(
      subtitleEntries,
      apiKey,
      baseUrl,
      model,
      actualSourceLanguage,
      normalizedTargetLanguage
    );
    console.log("翻译后的字幕：", translatedEntries);
    console.log(`翻译完成，共处理 ${translatedEntries.length} 条字幕`);

    // 生成和上传翻译后的ASS
    console.log("生成并上传翻译后的字幕文件...");

    // 获取视频分辨率信息
    const videoResolution = await getVideoResolution(taskId, tasksCollection);
    const languageSpecificStyle = getLanguageSpecificStyle(
      normalizedTargetLanguage,
      videoResolution.width,
      videoResolution.height
    );
    console.log(
      `使用${normalizedTargetLanguage}语言优化样式: 动态字体${languageSpecificStyle.fontSize}px, 边距${languageSpecificStyle.marginV}px, 亮黄色字体+黑色边框, 分辨率${videoResolution.width}x${videoResolution.height}(${videoResolution.source})`
    );
    const translatedAssContent = generateASS(
      translatedEntries,
      normalizedTargetLanguage,
      languageSpecificStyle
    );
    const uploadResult = await uploadTranslatedAssToOSS(taskId, translatedAssContent);

    // 更新任务状态为merging，准备字幕烧录
    await tasksCollection.doc(taskId).update({
      status: "merging",
      subtitleOssUrl: uploadResult.subtitleOssUrl,
      translationStarted: false,
      updateTime: new Date(),
    });

    console.log("翻译完成，准备启动字幕烧录");

    // 直接启动字幕烧录
    try {
      const mergeResult = await uniCloud.callFunction({
        name: "process-video-task",
        data: { taskId, action: "merge_subtitle" },
      });
      console.log("字幕烧录启动结果：", mergeResult.result);
    } catch (error) {
      console.error("字幕烧录启动异常：", error.message);
    }

    const processingTime = (Date.now() - startTime) / 1000;
    console.log(`字幕翻译任务完成，耗时: ${processingTime.toFixed(2)}秒`);

    return createSuccessResponse("字幕翻译成功", {
      taskId,
      status: "completed",
      translatedCount: translatedEntries.length,
      subtitleOssUrl: uploadResult.subtitleOssUrl,
      processingTime,
      sourceLanguage: actualSourceLanguage,
      targetLanguage: normalizedTargetLanguage,
    });
  } catch (error) {
    const processingTime = (Date.now() - startTime) / 1000;
    console.error("subtitle-translation-gpt 云函数执行错误：", {
      error: error.message,
      stack: error.stack,
      processingTime: `${processingTime.toFixed(2)}秒`,
    });

    return createErrorResponse(500, "字幕翻译失败: " + error.message, {
      processingTime,
    });
  }
};
